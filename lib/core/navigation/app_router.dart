import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../../features/items/presentation/screens/items_screen.dart';
import '../../features/transactions/presentation/screens/new_transaction_screen.dart';
import '../../features/transactions/presentation/screens/unpaid_transactions_screen.dart';
import '../../features/transaction_history/presentation/screens/transaction_history_screen.dart';
import '../../presentation/screens/main_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppConstants.itemsRoute,
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MainScreen(child: child);
        },
        routes: [
          GoRoute(
            path: AppConstants.itemsRoute,
            name: 'items',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const ItemsScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.newTransactionRoute,
            name: 'newTransaction',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const NewTransactionScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.unpaidTransactionsRoute,
            name: 'unpaidTransactions',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const UnpaidTransactionsScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.transactionHistoryRoute,
            name: 'transactionHistory',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const TransactionHistoryScreen(),
            ),
          ),
        ],
      ),
    ],
  );
});
