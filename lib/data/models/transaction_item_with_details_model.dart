import '../../domain/entities/transaction_item_with_details.dart';
import '../../core/datasources/local/database/database.dart';
import 'transaction_item_model.dart';
import 'item_model.dart';

extension TransactionItemWithDetailsMapper on TransactionItemWithDetails {
  TransactionItemWithDetailsEntity toEntity() {
    return TransactionItemWithDetailsEntity(
      transactionItem: transactionItem.toEntity(),
      item: item.toEntity(),
    );
  }
}
