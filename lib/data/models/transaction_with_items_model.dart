import '../../domain/entities/transaction_with_items.dart';
import '../../core/datasources/local/database/database.dart';
import 'transaction_model.dart';
import 'transaction_item_with_details_model.dart';

extension TransactionWithItemsMapper on TransactionWithItems {
  TransactionWithItemsEntity toEntity() {
    return TransactionWithItemsEntity(
      transaction: transaction.toEntity(),
      items: items.map((item) => item.toEntity()).toList(),
    );
  }
}
