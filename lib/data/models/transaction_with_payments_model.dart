import '../../domain/entities/transaction_with_payments.dart';
import '../../core/datasources/local/database/database.dart';
import 'transaction_model.dart';
import 'payment_model.dart';

extension TransactionWithPaymentsMapper on TransactionWithPayments {
  TransactionWithPaymentsEntity toEntity() {
    return TransactionWithPaymentsEntity(
      transaction: transaction.toEntity(),
      payments: payments.map((payment) => payment.toEntity()).toList(),
    );
  }
}
