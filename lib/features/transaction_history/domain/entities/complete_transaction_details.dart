import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../transactions/domain/entities/transaction.dart';
import '../../../transactions/domain/entities/transaction_item_with_details.dart';
import '../../../transactions/domain/entities/payment.dart';

part 'complete_transaction_details.freezed.dart';
part 'complete_transaction_details.g.dart';

@freezed
class CompleteTransactionDetailsEntity with _$CompleteTransactionDetailsEntity {
  const factory CompleteTransactionDetailsEntity({
    required TransactionEntity transaction,
    required List<TransactionItemWithDetailsEntity> items,
    required List<PaymentEntity> payments,
  }) = _CompleteTransactionDetailsEntity;

  factory CompleteTransactionDetailsEntity.fromJson(Map<String, dynamic> json) => _$CompleteTransactionDetailsEntityFromJson(json);
}
