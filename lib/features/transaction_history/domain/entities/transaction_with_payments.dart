import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../transactions/domain/entities/transaction.dart';
import '../../../transactions/domain/entities/payment.dart';

part 'transaction_with_payments.freezed.dart';
part 'transaction_with_payments.g.dart';

@freezed
class TransactionWithPaymentsEntity with _$TransactionWithPaymentsEntity {
  const factory TransactionWithPaymentsEntity({
    required TransactionEntity transaction,
    required List<PaymentEntity> payments,
  }) = _TransactionWithPaymentsEntity;

  factory TransactionWithPaymentsEntity.fromJson(Map<String, dynamic> json) => _$TransactionWithPaymentsEntityFromJson(json);
}
