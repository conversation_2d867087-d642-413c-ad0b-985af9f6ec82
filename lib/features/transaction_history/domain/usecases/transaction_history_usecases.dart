import '../entities/transaction_with_payments.dart';
import '../entities/complete_transaction_details.dart';
import '../../../transactions/domain/entities/transaction.dart';
import '../../../transactions/domain/entities/paid_item.dart';
import '../../../transactions/domain/repositories/transaction_repository.dart';

class GetPaidTransactionsUseCase {
  final TransactionRepository repository;

  GetPaidTransactionsUseCase(this.repository);

  Future<List<TransactionEntity>> call() {
    return repository.getPaidTransactions();
  }
}

class GetTransactionWithPaymentsUseCase {
  final TransactionRepository repository;

  GetTransactionWithPaymentsUseCase(this.repository);

  Future<TransactionWithPaymentsEntity> call(int transactionId) {
    return repository.getTransactionWithPayments(transactionId);
  }
}

class GetCompleteTransactionDetailsUseCase {
  final TransactionRepository repository;

  GetCompleteTransactionDetailsUseCase(this.repository);

  Future<CompleteTransactionDetailsEntity> call(int transactionId) {
    return repository.getCompleteTransactionDetails(transactionId);
  }
}

class GetPaymentPaidItemsUseCase {
  final TransactionRepository repository;

  GetPaymentPaidItemsUseCase(this.repository);

  Future<List<PaidItemEntity>> call(int paymentId) {
    return repository.getPaymentPaidItems(paymentId);
  }
}
