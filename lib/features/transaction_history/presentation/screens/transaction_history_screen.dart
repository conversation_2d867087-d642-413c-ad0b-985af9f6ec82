import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../providers/transaction_history_provider.dart' as provider;
import '../../../../core/widgets/empty_state.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import 'widgets/payment_history_expandable_card.dart';

class TransactionHistoryScreen extends ConsumerWidget {
  const TransactionHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleTransactionHistory),
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => ref.read(provider.baseTransactionHistoryProvider.notifier).refreshTransactions(),
              child: Consumer(
                builder: (context, ref, child) {
                  final groupedTransactionsState = ref.watch(provider.paymentDateGroupedTransactionsProvider);

                  return groupedTransactionsState.when(
                    data: (groupedTransactions) {
                      if (groupedTransactions.isEmpty) {
                        return const EmptyState(
                          message: AppConstants.emptyTransactionHistory,
                          icon: Icons.history_outlined,
                        );
                      }

                      // Sort dates in descending order (most recent first)
                      final sortedDates = groupedTransactions.keys.toList()
                        ..sort((a, b) => b.compareTo(a));

                      return ListView.builder(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        itemCount: sortedDates.length,
                        itemBuilder: (context, index) {
                          final date = sortedDates[index];
                          final dateTransactions = groupedTransactions[date]!;

                          // Calculate total payment amount for this date
                          double totalPaymentAmount = 0;
                          for (final transaction in dateTransactions) {
                            for (final payment in transaction.payments) {
                              // Only count payments made on this date
                              if (DateTime(payment.date.year, payment.date.month, payment.date.day).isAtSameMomentAs(date)) {
                                totalPaymentAmount += payment.amount;
                              }
                            }
                          }

                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: AppConstants.cardMarginBottom,
                              top: index == 0 ? 0 : AppConstants.smallPadding,
                            ),
                            child: PaymentHistoryExpandableCard(
                              date: date,
                              totalPaymentAmount: totalPaymentAmount,
                              transactions: dateTransactions,
                            ),
                          );
                        },
                      );
                    },
                    loading: () => const LoadingIndicator(),
                    error: (error, stackTrace) => ErrorDisplay(
                      message: 'Error loading transaction history: $error',
                      onRetry: () => ref.read(provider.baseTransactionHistoryProvider.notifier).refreshTransactions(),
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      );
  }
}
