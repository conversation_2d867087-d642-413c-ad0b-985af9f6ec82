import 'package:freezed_annotation/freezed_annotation.dart';

part 'transaction_item.freezed.dart';
part 'transaction_item.g.dart';

@freezed
class TransactionItemEntity with _$TransactionItemEntity {
  const factory TransactionItemEntity({
    required int id,
    required int transactionId,
    required int itemId,
    required int quantity,
    required double priceAtPurchase,
    required double remainingAmount,
    required DateTime createdAt,
  }) = _TransactionItemEntity;

  factory TransactionItemEntity.fromJson(Map<String, dynamic> json) => _$TransactionItemEntityFromJson(json);
}
