// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

TransactionItemEntity _$TransactionItemEntityFromJson(
  Map<String, dynamic> json,
) {
  return _TransactionItemEntity.fromJson(json);
}

/// @nodoc
mixin _$TransactionItemEntity {
  int get id => throw _privateConstructorUsedError;
  int get transactionId => throw _privateConstructorUsedError;
  int get itemId => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  double get priceAtPurchase => throw _privateConstructorUsedError;
  double get remainingAmount => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Serializes this TransactionItemEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TransactionItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionItemEntityCopyWith<TransactionItemEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionItemEntityCopyWith<$Res> {
  factory $TransactionItemEntityCopyWith(
    TransactionItemEntity value,
    $Res Function(TransactionItemEntity) then,
  ) = _$TransactionItemEntityCopyWithImpl<$Res, TransactionItemEntity>;
  @useResult
  $Res call({
    int id,
    int transactionId,
    int itemId,
    int quantity,
    double priceAtPurchase,
    double remainingAmount,
    DateTime createdAt,
  });
}

/// @nodoc
class _$TransactionItemEntityCopyWithImpl<
  $Res,
  $Val extends TransactionItemEntity
>
    implements $TransactionItemEntityCopyWith<$Res> {
  _$TransactionItemEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionId = null,
    Object? itemId = null,
    Object? quantity = null,
    Object? priceAtPurchase = null,
    Object? remainingAmount = null,
    Object? createdAt = null,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as int,
            transactionId:
                null == transactionId
                    ? _value.transactionId
                    : transactionId // ignore: cast_nullable_to_non_nullable
                        as int,
            itemId:
                null == itemId
                    ? _value.itemId
                    : itemId // ignore: cast_nullable_to_non_nullable
                        as int,
            quantity:
                null == quantity
                    ? _value.quantity
                    : quantity // ignore: cast_nullable_to_non_nullable
                        as int,
            priceAtPurchase:
                null == priceAtPurchase
                    ? _value.priceAtPurchase
                    : priceAtPurchase // ignore: cast_nullable_to_non_nullable
                        as double,
            remainingAmount:
                null == remainingAmount
                    ? _value.remainingAmount
                    : remainingAmount // ignore: cast_nullable_to_non_nullable
                        as double,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TransactionItemEntityImplCopyWith<$Res>
    implements $TransactionItemEntityCopyWith<$Res> {
  factory _$$TransactionItemEntityImplCopyWith(
    _$TransactionItemEntityImpl value,
    $Res Function(_$TransactionItemEntityImpl) then,
  ) = __$$TransactionItemEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int id,
    int transactionId,
    int itemId,
    int quantity,
    double priceAtPurchase,
    double remainingAmount,
    DateTime createdAt,
  });
}

/// @nodoc
class __$$TransactionItemEntityImplCopyWithImpl<$Res>
    extends
        _$TransactionItemEntityCopyWithImpl<$Res, _$TransactionItemEntityImpl>
    implements _$$TransactionItemEntityImplCopyWith<$Res> {
  __$$TransactionItemEntityImplCopyWithImpl(
    _$TransactionItemEntityImpl _value,
    $Res Function(_$TransactionItemEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionId = null,
    Object? itemId = null,
    Object? quantity = null,
    Object? priceAtPurchase = null,
    Object? remainingAmount = null,
    Object? createdAt = null,
  }) {
    return _then(
      _$TransactionItemEntityImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as int,
        transactionId:
            null == transactionId
                ? _value.transactionId
                : transactionId // ignore: cast_nullable_to_non_nullable
                    as int,
        itemId:
            null == itemId
                ? _value.itemId
                : itemId // ignore: cast_nullable_to_non_nullable
                    as int,
        quantity:
            null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                    as int,
        priceAtPurchase:
            null == priceAtPurchase
                ? _value.priceAtPurchase
                : priceAtPurchase // ignore: cast_nullable_to_non_nullable
                    as double,
        remainingAmount:
            null == remainingAmount
                ? _value.remainingAmount
                : remainingAmount // ignore: cast_nullable_to_non_nullable
                    as double,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TransactionItemEntityImpl implements _TransactionItemEntity {
  const _$TransactionItemEntityImpl({
    required this.id,
    required this.transactionId,
    required this.itemId,
    required this.quantity,
    required this.priceAtPurchase,
    required this.remainingAmount,
    required this.createdAt,
  });

  factory _$TransactionItemEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$TransactionItemEntityImplFromJson(json);

  @override
  final int id;
  @override
  final int transactionId;
  @override
  final int itemId;
  @override
  final int quantity;
  @override
  final double priceAtPurchase;
  @override
  final double remainingAmount;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'TransactionItemEntity(id: $id, transactionId: $transactionId, itemId: $itemId, quantity: $quantity, priceAtPurchase: $priceAtPurchase, remainingAmount: $remainingAmount, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionItemEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.priceAtPurchase, priceAtPurchase) ||
                other.priceAtPurchase == priceAtPurchase) &&
            (identical(other.remainingAmount, remainingAmount) ||
                other.remainingAmount == remainingAmount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    transactionId,
    itemId,
    quantity,
    priceAtPurchase,
    remainingAmount,
    createdAt,
  );

  /// Create a copy of TransactionItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionItemEntityImplCopyWith<_$TransactionItemEntityImpl>
  get copyWith =>
      __$$TransactionItemEntityImplCopyWithImpl<_$TransactionItemEntityImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$TransactionItemEntityImplToJson(this);
  }
}

abstract class _TransactionItemEntity implements TransactionItemEntity {
  const factory _TransactionItemEntity({
    required final int id,
    required final int transactionId,
    required final int itemId,
    required final int quantity,
    required final double priceAtPurchase,
    required final double remainingAmount,
    required final DateTime createdAt,
  }) = _$TransactionItemEntityImpl;

  factory _TransactionItemEntity.fromJson(Map<String, dynamic> json) =
      _$TransactionItemEntityImpl.fromJson;

  @override
  int get id;
  @override
  int get transactionId;
  @override
  int get itemId;
  @override
  int get quantity;
  @override
  double get priceAtPurchase;
  @override
  double get remainingAmount;
  @override
  DateTime get createdAt;

  /// Create a copy of TransactionItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionItemEntityImplCopyWith<_$TransactionItemEntityImpl>
  get copyWith => throw _privateConstructorUsedError;
}
