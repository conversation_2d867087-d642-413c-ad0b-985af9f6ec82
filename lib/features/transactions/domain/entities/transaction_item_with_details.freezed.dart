// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_item_with_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

TransactionItemWithDetailsEntity _$TransactionItemWithDetailsEntityFromJson(
  Map<String, dynamic> json,
) {
  return _TransactionItemWithDetailsEntity.fromJson(json);
}

/// @nodoc
mixin _$TransactionItemWithDetailsEntity {
  TransactionItemEntity get transactionItem =>
      throw _privateConstructorUsedError;
  ItemEntity get item => throw _privateConstructorUsedError;

  /// Serializes this TransactionItemWithDetailsEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionItemWithDetailsEntityCopyWith<TransactionItemWithDetailsEntity>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionItemWithDetailsEntityCopyWith<$Res> {
  factory $TransactionItemWithDetailsEntityCopyWith(
    TransactionItemWithDetailsEntity value,
    $Res Function(TransactionItemWithDetailsEntity) then,
  ) =
      _$TransactionItemWithDetailsEntityCopyWithImpl<
        $Res,
        TransactionItemWithDetailsEntity
      >;
  @useResult
  $Res call({TransactionItemEntity transactionItem, ItemEntity item});

  $TransactionItemEntityCopyWith<$Res> get transactionItem;
  $ItemEntityCopyWith<$Res> get item;
}

/// @nodoc
class _$TransactionItemWithDetailsEntityCopyWithImpl<
  $Res,
  $Val extends TransactionItemWithDetailsEntity
>
    implements $TransactionItemWithDetailsEntityCopyWith<$Res> {
  _$TransactionItemWithDetailsEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transactionItem = null, Object? item = null}) {
    return _then(
      _value.copyWith(
            transactionItem:
                null == transactionItem
                    ? _value.transactionItem
                    : transactionItem // ignore: cast_nullable_to_non_nullable
                        as TransactionItemEntity,
            item:
                null == item
                    ? _value.item
                    : item // ignore: cast_nullable_to_non_nullable
                        as ItemEntity,
          )
          as $Val,
    );
  }

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionItemEntityCopyWith<$Res> get transactionItem {
    return $TransactionItemEntityCopyWith<$Res>(_value.transactionItem, (
      value,
    ) {
      return _then(_value.copyWith(transactionItem: value) as $Val);
    });
  }

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ItemEntityCopyWith<$Res> get item {
    return $ItemEntityCopyWith<$Res>(_value.item, (value) {
      return _then(_value.copyWith(item: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransactionItemWithDetailsEntityImplCopyWith<$Res>
    implements $TransactionItemWithDetailsEntityCopyWith<$Res> {
  factory _$$TransactionItemWithDetailsEntityImplCopyWith(
    _$TransactionItemWithDetailsEntityImpl value,
    $Res Function(_$TransactionItemWithDetailsEntityImpl) then,
  ) = __$$TransactionItemWithDetailsEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TransactionItemEntity transactionItem, ItemEntity item});

  @override
  $TransactionItemEntityCopyWith<$Res> get transactionItem;
  @override
  $ItemEntityCopyWith<$Res> get item;
}

/// @nodoc
class __$$TransactionItemWithDetailsEntityImplCopyWithImpl<$Res>
    extends
        _$TransactionItemWithDetailsEntityCopyWithImpl<
          $Res,
          _$TransactionItemWithDetailsEntityImpl
        >
    implements _$$TransactionItemWithDetailsEntityImplCopyWith<$Res> {
  __$$TransactionItemWithDetailsEntityImplCopyWithImpl(
    _$TransactionItemWithDetailsEntityImpl _value,
    $Res Function(_$TransactionItemWithDetailsEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transactionItem = null, Object? item = null}) {
    return _then(
      _$TransactionItemWithDetailsEntityImpl(
        transactionItem:
            null == transactionItem
                ? _value.transactionItem
                : transactionItem // ignore: cast_nullable_to_non_nullable
                    as TransactionItemEntity,
        item:
            null == item
                ? _value.item
                : item // ignore: cast_nullable_to_non_nullable
                    as ItemEntity,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TransactionItemWithDetailsEntityImpl
    implements _TransactionItemWithDetailsEntity {
  const _$TransactionItemWithDetailsEntityImpl({
    required this.transactionItem,
    required this.item,
  });

  factory _$TransactionItemWithDetailsEntityImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$TransactionItemWithDetailsEntityImplFromJson(json);

  @override
  final TransactionItemEntity transactionItem;
  @override
  final ItemEntity item;

  @override
  String toString() {
    return 'TransactionItemWithDetailsEntity(transactionItem: $transactionItem, item: $item)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionItemWithDetailsEntityImpl &&
            (identical(other.transactionItem, transactionItem) ||
                other.transactionItem == transactionItem) &&
            (identical(other.item, item) || other.item == item));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, transactionItem, item);

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionItemWithDetailsEntityImplCopyWith<
    _$TransactionItemWithDetailsEntityImpl
  >
  get copyWith => __$$TransactionItemWithDetailsEntityImplCopyWithImpl<
    _$TransactionItemWithDetailsEntityImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransactionItemWithDetailsEntityImplToJson(this);
  }
}

abstract class _TransactionItemWithDetailsEntity
    implements TransactionItemWithDetailsEntity {
  const factory _TransactionItemWithDetailsEntity({
    required final TransactionItemEntity transactionItem,
    required final ItemEntity item,
  }) = _$TransactionItemWithDetailsEntityImpl;

  factory _TransactionItemWithDetailsEntity.fromJson(
    Map<String, dynamic> json,
  ) = _$TransactionItemWithDetailsEntityImpl.fromJson;

  @override
  TransactionItemEntity get transactionItem;
  @override
  ItemEntity get item;

  /// Create a copy of TransactionItemWithDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionItemWithDetailsEntityImplCopyWith<
    _$TransactionItemWithDetailsEntityImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}
