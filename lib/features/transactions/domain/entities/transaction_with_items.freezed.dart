// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_with_items.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

TransactionWithItemsEntity _$TransactionWithItemsEntityFromJson(
  Map<String, dynamic> json,
) {
  return _TransactionWithItemsEntity.fromJson(json);
}

/// @nodoc
mixin _$TransactionWithItemsEntity {
  TransactionEntity get transaction => throw _privateConstructorUsedError;
  List<TransactionItemWithDetailsEntity> get items =>
      throw _privateConstructorUsedError;

  /// Serializes this TransactionWithItemsEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionWithItemsEntityCopyWith<TransactionWithItemsEntity>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionWithItemsEntityCopyWith<$Res> {
  factory $TransactionWithItemsEntityCopyWith(
    TransactionWithItemsEntity value,
    $Res Function(TransactionWithItemsEntity) then,
  ) =
      _$TransactionWithItemsEntityCopyWithImpl<
        $Res,
        TransactionWithItemsEntity
      >;
  @useResult
  $Res call({
    TransactionEntity transaction,
    List<TransactionItemWithDetailsEntity> items,
  });

  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class _$TransactionWithItemsEntityCopyWithImpl<
  $Res,
  $Val extends TransactionWithItemsEntity
>
    implements $TransactionWithItemsEntityCopyWith<$Res> {
  _$TransactionWithItemsEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transaction = null, Object? items = null}) {
    return _then(
      _value.copyWith(
            transaction:
                null == transaction
                    ? _value.transaction
                    : transaction // ignore: cast_nullable_to_non_nullable
                        as TransactionEntity,
            items:
                null == items
                    ? _value.items
                    : items // ignore: cast_nullable_to_non_nullable
                        as List<TransactionItemWithDetailsEntity>,
          )
          as $Val,
    );
  }

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionEntityCopyWith<$Res> get transaction {
    return $TransactionEntityCopyWith<$Res>(_value.transaction, (value) {
      return _then(_value.copyWith(transaction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransactionWithItemsEntityImplCopyWith<$Res>
    implements $TransactionWithItemsEntityCopyWith<$Res> {
  factory _$$TransactionWithItemsEntityImplCopyWith(
    _$TransactionWithItemsEntityImpl value,
    $Res Function(_$TransactionWithItemsEntityImpl) then,
  ) = __$$TransactionWithItemsEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    TransactionEntity transaction,
    List<TransactionItemWithDetailsEntity> items,
  });

  @override
  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class __$$TransactionWithItemsEntityImplCopyWithImpl<$Res>
    extends
        _$TransactionWithItemsEntityCopyWithImpl<
          $Res,
          _$TransactionWithItemsEntityImpl
        >
    implements _$$TransactionWithItemsEntityImplCopyWith<$Res> {
  __$$TransactionWithItemsEntityImplCopyWithImpl(
    _$TransactionWithItemsEntityImpl _value,
    $Res Function(_$TransactionWithItemsEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transaction = null, Object? items = null}) {
    return _then(
      _$TransactionWithItemsEntityImpl(
        transaction:
            null == transaction
                ? _value.transaction
                : transaction // ignore: cast_nullable_to_non_nullable
                    as TransactionEntity,
        items:
            null == items
                ? _value._items
                : items // ignore: cast_nullable_to_non_nullable
                    as List<TransactionItemWithDetailsEntity>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TransactionWithItemsEntityImpl implements _TransactionWithItemsEntity {
  const _$TransactionWithItemsEntityImpl({
    required this.transaction,
    required final List<TransactionItemWithDetailsEntity> items,
  }) : _items = items;

  factory _$TransactionWithItemsEntityImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$TransactionWithItemsEntityImplFromJson(json);

  @override
  final TransactionEntity transaction;
  final List<TransactionItemWithDetailsEntity> _items;
  @override
  List<TransactionItemWithDetailsEntity> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'TransactionWithItemsEntity(transaction: $transaction, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionWithItemsEntityImpl &&
            (identical(other.transaction, transaction) ||
                other.transaction == transaction) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    transaction,
    const DeepCollectionEquality().hash(_items),
  );

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionWithItemsEntityImplCopyWith<_$TransactionWithItemsEntityImpl>
  get copyWith => __$$TransactionWithItemsEntityImplCopyWithImpl<
    _$TransactionWithItemsEntityImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransactionWithItemsEntityImplToJson(this);
  }
}

abstract class _TransactionWithItemsEntity
    implements TransactionWithItemsEntity {
  const factory _TransactionWithItemsEntity({
    required final TransactionEntity transaction,
    required final List<TransactionItemWithDetailsEntity> items,
  }) = _$TransactionWithItemsEntityImpl;

  factory _TransactionWithItemsEntity.fromJson(Map<String, dynamic> json) =
      _$TransactionWithItemsEntityImpl.fromJson;

  @override
  TransactionEntity get transaction;
  @override
  List<TransactionItemWithDetailsEntity> get items;

  /// Create a copy of TransactionWithItemsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionWithItemsEntityImplCopyWith<_$TransactionWithItemsEntityImpl>
  get copyWith => throw _privateConstructorUsedError;
}
