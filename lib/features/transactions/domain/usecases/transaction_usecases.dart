import '../entities/transaction.dart';
import '../entities/transaction_with_items.dart';
import '../entities/transaction_with_payments.dart';
import '../entities/complete_transaction_details.dart';
import '../entities/transaction_item.dart';
import '../entities/transaction_item_with_details.dart';
import '../entities/payment.dart';
import '../entities/paid_item.dart';
import '../repositories/transaction_repository.dart';

class GetAllTransactionsUseCase {
  final TransactionRepository repository;

  GetAllTransactionsUseCase(this.repository);

  Future<List<TransactionEntity>> call() {
    return repository.getAllTransactions();
  }
}

class GetUnpaidTransactionsUseCase {
  final TransactionRepository repository;

  GetUnpaidTransactionsUseCase(this.repository);

  Future<List<TransactionEntity>> call() {
    return repository.getUnpaidTransactions();
  }
}

class GetPaidTransactionsUseCase {
  final TransactionRepository repository;

  GetPaidTransactionsUseCase(this.repository);

  Future<List<TransactionEntity>> call() {
    return repository.getPaidTransactions();
  }
}

class GetTransactionByIdUseCase {
  final TransactionRepository repository;

  GetTransactionByIdUseCase(this.repository);

  Future<TransactionEntity> call(int id) {
    return repository.getTransactionById(id);
  }
}

class GetTransactionWithItemsUseCase {
  final TransactionRepository repository;

  GetTransactionWithItemsUseCase(this.repository);

  Future<TransactionWithItemsEntity> call(int transactionId) {
    return repository.getTransactionWithItems(transactionId);
  }
}

class GetTransactionWithPaymentsUseCase {
  final TransactionRepository repository;

  GetTransactionWithPaymentsUseCase(this.repository);

  Future<TransactionWithPaymentsEntity> call(int transactionId) {
    return repository.getTransactionWithPayments(transactionId);
  }
}

class GetCompleteTransactionDetailsUseCase {
  final TransactionRepository repository;

  GetCompleteTransactionDetailsUseCase(this.repository);

  Future<CompleteTransactionDetailsEntity> call(int transactionId) {
    return repository.getCompleteTransactionDetails(transactionId);
  }
}

class CreateTransactionWithItemsUseCase {
  final TransactionRepository repository;

  CreateTransactionWithItemsUseCase(this.repository);

  Future<int> call(
    TransactionEntity transaction,
    List<TransactionItemEntity> items,
  ) {
    return repository.createTransactionWithItems(transaction, items);
  }
}

class AddPaymentAndUpdateTransactionUseCase {
  final TransactionRepository repository;

  AddPaymentAndUpdateTransactionUseCase(this.repository);

  Future<int> call(
    PaymentEntity payment,
    TransactionEntity updatedTransaction,
    [List<PaidItemEntity>? paidItems]
  ) {
    return repository.addPaymentAndUpdateTransaction(
      payment,
      updatedTransaction,
      paidItems ?? []
    );
  }
}

class GetPaymentPaidItemsUseCase {
  final TransactionRepository repository;

  GetPaymentPaidItemsUseCase(this.repository);

  Future<List<PaidItemEntity>> call(int paymentId) {
    return repository.getPaymentPaidItems(paymentId);
  }
}

class GetPaidItemsForTransactionItemUseCase {
  final TransactionRepository repository;

  GetPaidItemsForTransactionItemUseCase(this.repository);

  Future<List<PaidItemEntity>> call(int transactionItemId) {
    return repository.getPaidItemsForTransactionItem(transactionItemId);
  }
}

class GetUnpaidTransactionItemsUseCase {
  final TransactionRepository repository;

  GetUnpaidTransactionItemsUseCase(this.repository);

  Future<List<TransactionItemWithDetailsEntity>> call() {
    return repository.getUnpaidTransactionItems();
  }
}

class UpdateTransactionItemRemainingAmountUseCase {
  final TransactionRepository repository;

  UpdateTransactionItemRemainingAmountUseCase(this.repository);

  Future<void> call(int transactionItemId, double newRemainingAmount) {
    return repository.updateTransactionItemRemainingAmount(transactionItemId, newRemainingAmount);
  }
}

class RecalculateTransactionRemainingAmountUseCase {
  final TransactionRepository repository;

  RecalculateTransactionRemainingAmountUseCase(this.repository);

  Future<void> call(int transactionId) {
    return repository.recalculateTransactionRemainingAmount(transactionId);
  }
}

class UpdateTransactionUseCase {
  final TransactionRepository repository;

  UpdateTransactionUseCase(this.repository);

  Future<bool> call(TransactionEntity transaction) {
    return repository.updateTransaction(transaction);
  }
}

class DeleteTransactionUseCase {
  final TransactionRepository repository;

  DeleteTransactionUseCase(this.repository);

  Future<int> call(int transactionId) {
    return repository.deleteTransaction(transactionId);
  }
}
