import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../providers/new_transaction_provider.dart';
import '../../../../core/widgets/confirmation_dialog.dart';
import '../../../../core/widgets/loading_indicator.dart';
import 'widgets/transaction_item_card.dart';
import 'widgets/add_transaction_item_bottom_sheet.dart';

class NewTransactionScreen extends ConsumerWidget {
  const NewTransactionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionState = ref.watch(newTransactionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleNewTransaction),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddItemBottomSheet(context, ref),
            tooltip: 'Add Item',
          ),
        ],
      ),
      body: transactionState.isLoading
          ? const LoadingIndicator()
          : RefreshIndicator(
              onRefresh: () async {
                ref.read(newTransactionProvider.notifier).resetTransaction();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDateSelector(context, ref, transactionState),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildItemsList(context, ref, transactionState),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildTotalAmount(context, transactionState),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildCreateButton(context, ref, transactionState),
                  ],
                ),
              ),
            ),

    );
  }

  Widget _buildDateSelector(
    BuildContext context,
    WidgetRef ref,
    NewTransactionState state,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppConstants.labelDate,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            InkWell(
              onTap: () => _selectDate(context, ref),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.smallPadding,
                  horizontal: AppConstants.defaultPadding,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      Formatters.formatDate(state.date),
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const Icon(Icons.calendar_today),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList(
    BuildContext context,
    WidgetRef ref,
    NewTransactionState state,
  ) {
    if (state.items.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Column(
              children: [
                const Icon(
                  Icons.shopping_cart_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'No items added yet',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: AppConstants.smallPadding),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: state.items.length,
          itemBuilder: (context, index) {
            final item = state.items[index];
            return TransactionItemCard(
              item: item.item,
              quantity: item.quantity,
              onRemove: () => ref.read(newTransactionProvider.notifier).removeItem(index),
              onQuantityChanged: (newQuantity) {
                ref.read(newTransactionProvider.notifier).updateItemQuantity(index, newQuantity);
              },
            );
          },
        ),
        const SizedBox(height: AppConstants.smallPadding),
      ],
    );
  }

  Widget _buildTotalAmount(BuildContext context, NewTransactionState state) {
    return Card(
      color: Theme.of(context).colorScheme.primary.withAlpha(26),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppConstants.labelTotalAmount,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              Formatters.formatCurrency(state.totalAmount),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton(
    BuildContext context,
    WidgetRef ref,
    NewTransactionState state,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: state.items.isEmpty
            ? null
            : () => _confirmCreateTransaction(context, ref),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text(AppConstants.buttonCreateTransaction),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, WidgetRef ref) async {
    final currentDate = ref.read(newTransactionProvider).date;
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );

    if (selectedDate != null) {
      ref.read(newTransactionProvider.notifier).setDate(selectedDate);
    }
  }

  void _showAddItemBottomSheet(BuildContext context, WidgetRef ref) {
    // Use a try-catch block to handle potential navigation errors
    try {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        useSafeArea: true,
        isDismissible: true,
        enableDrag: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppConstants.bottomSheetBorderRadius),
          ),
        ),
        builder: (context) => const AddTransactionItemBottomSheet(),
      );
    } catch (e) {
      debugPrint('Error showing bottom sheet: $e');
    }
  }

  Future<void> _confirmCreateTransaction(BuildContext context, WidgetRef ref) async {
    final confirmed = await ConfirmationDialog.show(
      context: context,
      title: 'Create Transaction',
      message: AppConstants.confirmCreateTransaction,
      confirmLabel: AppConstants.buttonCreateTransaction,
    );

    if (confirmed && context.mounted) {
      final success = await ref.read(newTransactionProvider.notifier).createTransaction();

      if (success && context.mounted) {
        // Navigate to the unpaid transactions screen after successful transaction creation
        if (context.mounted) {
          // Use GoRouter for navigation instead of Navigator
          GoRouter.of(context).go(AppConstants.unpaidTransactionsRoute);

          // Show success message after navigation
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text(AppConstants.successTransactionCreated)),
          );
        }
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to create transaction')),
        );
      }
    }
  }
}
