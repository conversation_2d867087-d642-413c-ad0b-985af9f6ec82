import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/utils/validators.dart';
import '../../../providers/unpaid_transactions_provider.dart';
import '../../../widgets/currency_text_field.dart';

class PaymentBottomSheet extends ConsumerStatefulWidget {
  final int transactionId;

  const PaymentBottomSheet({
    super.key,
    required this.transactionId,
  });

  @override
  ConsumerState<PaymentBottomSheet> createState() => _PaymentBottomSheetState();
}

class _PaymentBottomSheetState extends ConsumerState<PaymentBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  bool _isLoading = false;
  double _remainingAmount = 0;

  @override
  void initState() {
    super.initState();
    _loadTransactionDetails();
  }

  Future<void> _loadTransactionDetails() async {
    final transactionsState = ref.read(unpaidTransactionsProvider);
    if (transactionsState is AsyncData && transactionsState.value != null) {
      final transactionGroup = transactionsState.value!.firstWhere(
        (group) => group.transaction.id == widget.transactionId,
        orElse: () => throw Exception('Transaction not found'),
      );

      setState(() {
        _remainingAmount = transactionGroup.totalUnpaidAmount;
        // Set the default payment amount to the full remaining amount
        _amountController.text = _remainingAmount.toString();
      });
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _makePayment() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final amount = Formatters.parseCurrency(_amountController.text);

    final success = await ref.read(unpaidTransactionsProvider.notifier).addPayment(
      widget.transactionId,
      amount,
    );

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppConstants.successPaymentAdded)),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to add payment')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              AppConstants.titlePayment,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Remaining Amount: ${Formatters.formatCurrency(_remainingAmount)}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CurrencyTextField(
              controller: _amountController,
              label: AppConstants.labelPaymentAmount,
              validator: (value) => Validators.validatePaymentAmount(value, _remainingAmount),
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: _isLoading ? null : _makePayment,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(AppConstants.buttonPay),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppConstants.buttonCancel),
            ),
          ],
        ),
      ),
    );
  }
}
