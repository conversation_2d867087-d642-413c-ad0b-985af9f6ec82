import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/datasources/local/database/database.dart';
import '../../data/repositories/item_repository_impl.dart';
import '../../data/repositories/transaction_repository_impl.dart';
import '../../domain/repositories/item_repository.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../../domain/usecases/item_usecases.dart';
import '../../domain/usecases/transaction_usecases.dart';

// Database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

// Repository providers
final itemRepositoryProvider = Provider<ItemRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return ItemRepositoryImpl(database);
});

final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return TransactionRepositoryImpl(database);
});

// Item use case providers
final getAllItemsUseCaseProvider = Provider<GetAllItemsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return GetAllItemsUseCase(repository);
});

final searchItemsUseCaseProvider = Provider<SearchItemsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return SearchItemsUseCase(repository);
});

final getItemByIdUseCaseProvider = Provider<GetItemByIdUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return GetItemByIdUseCase(repository);
});

final isItemNameExistsUseCaseProvider = Provider<IsItemNameExistsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return IsItemNameExistsUseCase(repository);
});

final addItemUseCaseProvider = Provider<AddItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return AddItemUseCase(repository);
});

final updateItemUseCaseProvider = Provider<UpdateItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return UpdateItemUseCase(repository);
});

final deleteItemUseCaseProvider = Provider<DeleteItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return DeleteItemUseCase(repository);
});

// Transaction use case providers
final getAllTransactionsUseCaseProvider = Provider<GetAllTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetAllTransactionsUseCase(repository);
});

final getUnpaidTransactionsUseCaseProvider = Provider<GetUnpaidTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetUnpaidTransactionsUseCase(repository);
});

final getPaidTransactionsUseCaseProvider = Provider<GetPaidTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaidTransactionsUseCase(repository);
});

final getTransactionByIdUseCaseProvider = Provider<GetTransactionByIdUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionByIdUseCase(repository);
});

final getTransactionWithItemsUseCaseProvider = Provider<GetTransactionWithItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionWithItemsUseCase(repository);
});

final getTransactionWithPaymentsUseCaseProvider = Provider<GetTransactionWithPaymentsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionWithPaymentsUseCase(repository);
});

final getCompleteTransactionDetailsUseCaseProvider = Provider<GetCompleteTransactionDetailsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetCompleteTransactionDetailsUseCase(repository);
});

final createTransactionWithItemsUseCaseProvider = Provider<CreateTransactionWithItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return CreateTransactionWithItemsUseCase(repository);
});

final addPaymentAndUpdateTransactionUseCaseProvider = Provider<AddPaymentAndUpdateTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return AddPaymentAndUpdateTransactionUseCase(repository);
});

final getPaymentPaidItemsUseCaseProvider = Provider<GetPaymentPaidItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaymentPaidItemsUseCase(repository);
});

final getPaidItemsForTransactionItemUseCaseProvider = Provider<GetPaidItemsForTransactionItemUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaidItemsForTransactionItemUseCase(repository);
});

final getUnpaidTransactionItemsUseCaseProvider = Provider<GetUnpaidTransactionItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetUnpaidTransactionItemsUseCase(repository);
});

final updateTransactionItemRemainingAmountUseCaseProvider = Provider<UpdateTransactionItemRemainingAmountUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return UpdateTransactionItemRemainingAmountUseCase(repository);
});

final recalculateTransactionRemainingAmountUseCaseProvider = Provider<RecalculateTransactionRemainingAmountUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return RecalculateTransactionRemainingAmountUseCase(repository);
});

final updateTransactionUseCaseProvider = Provider<UpdateTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return UpdateTransactionUseCase(repository);
});

final deleteTransactionUseCaseProvider = Provider<DeleteTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return DeleteTransactionUseCase(repository);
});
