import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

class Formatters {
  // Private constructor to prevent instantiation
  Formatters._();

  // Currency formatter - without decimal places
  static String formatCurrency(double amount) {
    // Round to nearest integer
    final roundedAmount = amount.round();
    final formatter = NumberFormat(AppConstants.currencyFormat, 'id_ID');
    return '${AppConstants.currencySymbol} ${formatter.format(roundedAmount)}';
  }

  // Date formatter
  static String formatDate(DateTime date) {
    return DateFormat(AppConstants.dateFormat).format(date);
  }

  // Date-time formatter
  static String formatDateTime(DateTime dateTime) {
    return DateFormat(AppConstants.dateTimeFormat).format(dateTime);
  }

  // Parse currency string to double (whole numbers only)
  static double parseCurrency(String currencyString) {
    // Remove currency symbol and all non-numeric characters (including decimal point)
    final cleanString = currencyString
        .replaceAll(AppConstants.currencySymbol, '')
        .replaceAll(RegExp(r'[^\d]'), '');

    return double.tryParse(cleanString) ?? 0.0;
  }
}
