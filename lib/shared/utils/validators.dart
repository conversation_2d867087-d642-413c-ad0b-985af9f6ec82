import '../constants/app_constants.dart';

class Validators {
  // Private constructor to prevent instantiation
  Validators._();

  // Required field validator
  static String? validateRequired(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.errorRequiredField;
    }
    return null;
  }

  // Item name validator
  static String? validateItemName(String? value) {
    final requiredError = validateRequired(value);
    if (requiredError != null) {
      return requiredError;
    }

    if (value!.trim().length < AppConstants.minItemNameLength) {
      return AppConstants.errorMinLength;
    }

    // Check for invalid characters (allow letters, numbers, spaces, and basic punctuation)
    final validCharacters = RegExp(r'^[a-zA-Z0-9\s.,\-_()]+$');
    if (!validCharacters.hasMatch(value.trim())) {
      return AppConstants.errorInvalidCharacters;
    }

    return null;
  }

  // Price validator (whole numbers only)
  static String? validatePrice(String? value) {
    final requiredError = validateRequired(value);
    if (requiredError != null) {
      return requiredError;
    }

    // Remove currency symbol and formatting for validation (digits only)
    final cleanValue = value!.replaceAll(AppConstants.currencySymbol, '').replaceAll(RegExp(r'[^\d]'), '');

    final price = int.tryParse(cleanValue);
    if (price == null) {
      return AppConstants.errorInvalidNumber;
    }

    if (price <= 0) {
      return AppConstants.errorMinPrice;
    }

    return null;
  }

  // Quantity validator
  static String? validateQuantity(String? value) {
    final requiredError = validateRequired(value);
    if (requiredError != null) {
      return requiredError;
    }

    final quantity = int.tryParse(value!);
    if (quantity == null) {
      return AppConstants.errorInvalidNumber;
    }

    if (quantity < AppConstants.minQuantity) {
      return AppConstants.errorMinQuantity;
    }

    return null;
  }

  // Payment amount validator (whole numbers only)
  static String? validatePaymentAmount(String? value, double remainingAmount) {
    final requiredError = validateRequired(value);
    if (requiredError != null) {
      return requiredError;
    }

    // Remove currency symbol and formatting for validation (digits only)
    final cleanValue = value!.replaceAll(AppConstants.currencySymbol, '').replaceAll(RegExp(r'[^\d]'), '');

    final amount = int.tryParse(cleanValue);
    if (amount == null) {
      return AppConstants.errorInvalidNumber;
    }

    if (amount <= 0) {
      return AppConstants.errorMinPrice;
    }

    // Round the remaining amount to the nearest integer for comparison
    final roundedRemainingAmount = remainingAmount.round();
    if (amount > roundedRemainingAmount) {
      return AppConstants.errorExceedingPayment;
    }

    return null;
  }
}
