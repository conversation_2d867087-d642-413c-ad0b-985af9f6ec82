import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmLabel;
  final String cancelLabel;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmColor;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmLabel = AppConstants.buttonConfirm,
    this.cancelLabel = AppConstants.buttonCancel,
    required this.onConfirm,
    this.onCancel,
    this.confirmColor,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () {
            if (onCancel != null) {
              onCancel!();
            } else {
              // Use maybePop to safely pop the dialog
              Navigator.of(context).maybePop(false);
            }
          },
          child: Text(cancelLabel),
        ),
        ElevatedButton(
          onPressed: () {
            onConfirm();
            // Use maybePop to safely pop the dialog
            Navigator.of(context).maybePop(true);
          },
          style: confirmColor != null
              ? ElevatedButton.styleFrom(backgroundColor: confirmColor)
              : null,
          child: Text(confirmLabel),
        ),
      ],
    );
  }

  static Future<bool> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmLabel = AppConstants.buttonConfirm,
    String cancelLabel = AppConstants.buttonCancel,
    Color? confirmColor,
  }) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false, // Prevent dismissing by tapping outside
        builder: (dialogContext) => ConfirmationDialog(
          title: title,
          message: message,
          confirmLabel: confirmLabel,
          cancelLabel: cancelLabel,
          confirmColor: confirmColor,
          onConfirm: () {
            if (dialogContext.mounted) {
              Navigator.of(dialogContext).maybePop(true);
            }
          },
          onCancel: () {
            if (dialogContext.mounted) {
              Navigator.of(dialogContext).maybePop(false);
            }
          },
        ),
      );
      return result ?? false;
    } catch (e) {
      debugPrint('Error showing confirmation dialog: $e');
      return false;
    }
  }
}
